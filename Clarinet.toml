[project]
name = 'clarity-nft-rental-marketplace'
description = ''
authors = []
telemetry = true
cache_dir = './.cache'
requirements = []
[contracts.admin]
path = 'contracts/admin.clar'
clarity_version = 3
epoch = 3.1

[contracts.collateral]
path = 'contracts/collateral.clar'
clarity_version = 3
epoch = 3.1

[contracts.nft-wrapper]
path = 'contracts/nft-wrapper.clar'
clarity_version = 3
epoch = 3.1

[contracts.pricing]
path = 'contracts/pricing.clar'
clarity_version = 3
epoch = 3.1

[contracts.rental-core]
path = 'contracts/rental-core.clar'
clarity_version = 3
epoch = 3.1

[contracts.treasury]
path = 'contracts/treasury.clar'
clarity_version = 3
epoch = 3.1

[contracts.utils]
path = 'contracts/utils.clar'
clarity_version = 3
epoch = 3.1

[contracts.views]
path = 'contracts/views.clar'
clarity_version = 3
epoch = 3.1
[repl.analysis]
passes = ['check_checker']

[repl.analysis.check_checker]
strict = false
trusted_sender = false
trusted_caller = false
callee_filter = false
